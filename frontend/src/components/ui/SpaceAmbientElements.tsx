import { useEffect, useState } from 'react'

interface FloatingElement {
  id: number
  x: number
  y: number
  size: number
  opacity: number
  duration: number
  delay: number
}

interface SpaceAmbientElementsProps {
  intensity?: 'low' | 'medium' | 'high'
  isActive?: boolean
}

export default function SpaceAmbientElements({ 
  intensity = 'medium', 
  isActive = true 
}: SpaceAmbientElementsProps) {
  const [elements, setElements] = useState<FloatingElement[]>([])

  const intensityConfig = {
    low: { count: 8, maxSize: 4, baseOpacity: 0.1 },
    medium: { count: 12, maxSize: 6, baseOpacity: 0.15 },
    high: { count: 16, maxSize: 8, baseOpacity: 0.2 }
  }

  const config = intensityConfig[intensity]

  useEffect(() => {
    const generateElements = () => {
      const newElements: FloatingElement[] = []
      
      for (let i = 0; i < config.count; i++) {
        newElements.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * config.maxSize + 2,
          opacity: Math.random() * config.baseOpacity + 0.05,
          duration: Math.random() * 20 + 15, // 15-35 seconds
          delay: Math.random() * 10 // 0-10 seconds delay
        })
      }
      
      setElements(newElements)
    }

    generateElements()
  }, [intensity, config.count, config.maxSize, config.baseOpacity])

  if (!isActive) return null

  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {/* Floating particles */}
      {elements.map((element) => (
        <div
          key={element.id}
          className="absolute rounded-full bg-gradient-to-r from-blue-400/20 to-purple-400/20 animate-float"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            width: `${element.size}px`,
            height: `${element.size}px`,
            opacity: element.opacity,
            animationDuration: `${element.duration}s`,
            animationDelay: `${element.delay}s`,
            filter: 'blur(1px)',
            boxShadow: `0 0 ${element.size * 2}px rgba(147, 197, 253, ${element.opacity * 0.5})`
          }}
        />
      ))}

      {/* Subtle light rays */}
      <div className="absolute inset-0">
        <div 
          className="absolute top-1/4 left-1/4 w-px h-32 bg-gradient-to-b from-transparent via-blue-400/10 to-transparent rotate-12 animate-pulse"
          style={{ animationDuration: '4s' }}
        />
        <div 
          className="absolute top-3/4 right-1/3 w-px h-24 bg-gradient-to-b from-transparent via-purple-400/10 to-transparent -rotate-12 animate-pulse"
          style={{ animationDuration: '6s', animationDelay: '2s' }}
        />
        <div 
          className="absolute top-1/2 right-1/4 w-px h-20 bg-gradient-to-b from-transparent via-cyan-400/10 to-transparent rotate-45 animate-pulse"
          style={{ animationDuration: '5s', animationDelay: '1s' }}
        />
      </div>

      {/* Distant stars effect */}
      <div className="absolute inset-0">
        {Array.from({ length: 20 }, (_, i) => (
          <div
            key={`star-${i}`}
            className="absolute w-0.5 h-0.5 bg-white/30 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDuration: `${Math.random() * 3 + 2}s`,
              animationDelay: `${Math.random() * 5}s`
            }}
          />
        ))}
      </div>
    </div>
  )
}
